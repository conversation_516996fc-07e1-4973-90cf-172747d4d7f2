﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Wallets;

public sealed record GetSettlementWalletAccessBalanceResponse(decimal Balance);

public sealed class GetSettlementWalletAccessBalanceController : ApiControllerBase
{
    [HttpGet("wallet/get-balance")]
    [Authorize("read")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetSettlementWalletAccessBalanceAsync()
    {
        var result =
            await Mediator.Send(new GetSettlementWalletAccessBalanceQuery(), HttpContext.RequestAborted);

        return result.Match(Ok, Problem);
    }
}

public sealed record GetSettlementWalletAccessBalanceQuery
    : IRequest<GetSettlementWalletAccessBalanceQuery, Task<ErrorOr<GetSettlementWalletAccessBalanceResponse>>>;

public sealed class GetSettlementWalletAccessBalanceQueryHandler(
    ICurrentUserService currentUserService,
    ApplicationDbContext dbContext,
    IWalletService walletService)
    : IRequestHandler<GetSettlementWalletAccessBalanceQuery, Task<ErrorOr<GetSettlementWalletAccessBalanceResponse>>>
{
    public async Task<ErrorOr<GetSettlementWalletAccessBalanceResponse>> Handle(
        GetSettlementWalletAccessBalanceQuery request,
        CancellationToken cancellationToken)
    {
        if (currentUserService.UserId is null or 0)
            return Error.NotFound(description: "شناسه کاربر یافت نشد.");

        var settlementWalletId = await dbContext.UserWalletInformations
            .AsNoTracking()
            .Where(x => x.UserId == currentUserService.UserId)
            .Select(x => x.SettlementWalletId)
            .FirstOrDefaultAsync(cancellationToken);

        if (settlementWalletId is null)
            return Error.NotFound(description: "کیف پول تسویه کاربر یافت نشد.");

        var getBalance = await walletService.GetWalletAccessBalanceById(
            settlementWalletId.Value,
            cancellationToken);

        if (getBalance.IsError)
            return getBalance.ErrorsOrEmptyList;

        return new GetSettlementWalletAccessBalanceResponse(getBalance.Value.AccessBalance);
    }
}
