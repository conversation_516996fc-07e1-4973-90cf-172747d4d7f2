﻿using ErrorOr;
using Microsoft.Extensions.Logging;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;
using Zify.Settlement.Application.Infrastructure.Services.EzPay.Models;

namespace Zify.Settlement.Application.Infrastructure.Services.EzPay;

public class EzPayService(
    IEzPayHttpClient client,
    ILogger<EzPayService> logger) : IEzPayService
{
    public async Task<ErrorOr<EzPayInquiryIbanResponse?>> InquiryIban(string iban)
    {
        var inquiry = await InquiryIbans([iban]);
        if (inquiry.IsError)
            return inquiry.Errors.First();

        return inquiry.Value.FirstOrDefault();
    }

    public async Task<ErrorOr<List<EzPayInquiryIbanResponse>>> InquiryIbans(string[] ibanList)
    {
        var clientResult = await client.InquiryIbans(ibanList);
        if (clientResult.IsSuccessful)
        {
            return clientResult.Content
                .ConvertAll(x =>
                    new EzPayInquiryIbanResponse(
                        x.Iban,
                        x.BankName,
                        IsActive: x.AccountStatus == "ACTIVE",
                        x.AccountOwners
                            .ConvertAll(y =>
                                new EzIbanAccountOwnerResponse(
                                    y.FirstName,
                                    y.LastName))));
        }
        logger.LogError(clientResult.Error, "Error inquiry IBAN - EzPay: {Error}", clientResult.Error.Content);
        return Error.Failure(description: "خطا در استعلام شبا");
    }
}
