﻿using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.Helpers;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.OrderDetails;

public sealed record AddBasicOrderDetailResponse();

public sealed class AddBasicOrderDetailController : ApiControllerBase
{
    [HttpPost("{orderId:guid}/add-basic-order-detail")]
    [Authorize("write")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AddOrderDetail(
        [FromRoute] Guid orderId,
        [FromBody] AddBasicOrderDetailCommand request)
    {
        request.OrderId = orderId;
        var result = await Mediator.Send(request, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record AddBasicOrderDetailCommand(
    decimal Amount,
    string? Iban,
    string? Description)
    : IRequest<AddBasicOrderDetailCommand, Task<ErrorOr<AddBasicOrderDetailResponse>>>
{
    [JsonIgnore]
    public Guid OrderId { get; set; }
}

public sealed class AddBasicOrderDetailCommandHandler(
    ApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    Lazy<IEzPayService> ezPayService)
    : IRequestHandler<AddBasicOrderDetailCommand, Task<ErrorOr<AddBasicOrderDetailResponse>>>
{
    public async Task<ErrorOr<AddBasicOrderDetailResponse>> Handle(
        AddBasicOrderDetailCommand request,
        CancellationToken cancellationToken)
    {
        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "کد درخواست نامعتبر می‌باشد");

        if (order.Status != OrderStatus.Draft)
            return Error.Forbidden(description: "با توجه به وضعیت درخواست افزودن تسویه امکان پذیر نیست");

        var userConfig = await dbContext.UserConfigs
            .AsNoTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig is null) return Error.NotFound(description: "اطلاعات کاربری یافت نشد");

        var iban = await GetUserIban(request, userConfig);
        if (iban.IsError)
            return iban.ErrorsOrEmptyList;

        var orderDetail = OrderDetail.Create(
            Iban.Of(iban.Value),
            request.Amount,
            userConfig.CalculateWage(request.Amount),
            request.Description);

        order.AddDetail(orderDetail);

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0
            ? new AddBasicOrderDetailResponse()
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }

    private async Task<ErrorOr<Iban>> GetUserIban(AddBasicOrderDetailCommand request, UserConfig userConfig)
    {
        var iban = Iban.Of(userConfig.Iban);
        if (string.IsNullOrWhiteSpace(request.Iban)) return iban;

        iban = Iban.Of(request.Iban);

        var inquiryIban = await ezPayService.Value.InquiryIban(iban);
        if (inquiryIban.IsError)
            return inquiryIban.ErrorsOrEmptyList;

        if (!inquiryIban.Value?.IsActive ?? false)
            return Error.Forbidden(description:
                $"حساب بانکی مربوط به شبای {iban} مسدود می‌باشد");

        return iban;
    }
}

public sealed class AddBasicOrderDetailCommandValidator : AbstractValidator<AddBasicOrderDetailCommand>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentUserService _currentUserService;

    public AddBasicOrderDetailCommandValidator(ApplicationDbContext dbContext, ICurrentUserService currentUserService)
    {
        _dbContext = dbContext;
        _currentUserService = currentUserService;

        WhenAsync(HasOneOrderDetail, () => throw new ReachedMaximumOrderDetailsCountException(1));

        RuleFor(x => x.Amount)
            .NotEmpty().WithMessage("مبلغ اجباری است")
            .GreaterThan(UserCheckOptions.GetMinSettlementAmount)
            .WithMessage("مبلغ تسویه کمتر از حد مجاز")
            .MustAsync(LessThanMaxSettlementAmount)
            .WithMessage("مبلغ تسویه بیشتر از حد مجاز");

        RuleFor(x => x.Description)
            .MaximumLength(50).WithMessage("متن توضیحات بیشتر از ۵۰ کارکتر نمی‌تواند باشد");

        RuleFor(x => x.OrderId).NotEmpty();
    }

    private Task<bool> HasOneOrderDetail(AddBasicOrderDetailCommand request, CancellationToken ct)
    {
        return _dbContext.OrderDetails
            .Where(x => x.OrderId == request.OrderId)
            .AnyAsync(ct);
    }

    private async Task<bool> LessThanMaxSettlementAmount(decimal amount, CancellationToken ct)
    {
        long max = await _dbContext.UserConfigs
            .AsNoTracking()
            .Where(x => x.UserId == _currentUserService.UserId)
            .Select(x => x.MaxSettlementAmount)
            .FirstOrDefaultAsync(ct);
        if (max == 0) max = UserCheckOptions.GetMaxSettlementAmount;
        return amount <= max;
    }
}