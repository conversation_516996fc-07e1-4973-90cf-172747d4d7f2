﻿using DispatchR.Extensions;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PayPing.User.AdminSDK.Configuration;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.GrpcClients;
using Zify.Settlement.Application.Infrastructure.Clients.Grpc;
using Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
using Zify.Settlement.Application.Infrastructure.Persistence;
using Zify.Settlement.Application.Infrastructure.Persistence.Interceptors;
using Zify.Settlement.Application.Infrastructure.Services;
using Zify.Settlement.Application.Infrastructure.Services.EzPay;
using Zify.Settlement.Application.Infrastructure.Services.Users;
using Zify.Settlement.Application.Infrastructure.Services.Wallet;
using Zify.Settlement.Application.Infrastructure.Utilities;

namespace Zify.Settlement.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddConfigurations(configuration);
        services.AddIdentity(configuration);
        services.AddGrpcClients(configuration);
        
        services.AddDispatchR(typeof(DependencyInjection).Assembly);
        services.AddValidatorsFromAssembly(typeof(DependencyInjection).Assembly, includeInternalTypes: true);
        return services;
    }

    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Register the Swagger generator with JWT support
        services.AddCustomSwagger();

        // Add API Versioning
        services.AddCustomVersioning();

        //Register Admin SDKs
        services.AddUserAdminSdk(
            new Uri(Environment.GetEnvironmentVariable("PayPing_UserServices_Address") ?? string.Empty));

        // Register services
        services.AddScoped<IUserProfileService, UserProfileService>();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddTransient<IDateTime, DateTimeService>();
        services.AddScoped<IEzPayService, EzPayService>();
        services.AddScoped<IWalletService, WalletService>();

        // Register Http Clients
        services.AddEzPayRefitClient(configuration);
        
        // Register Utilities
        services.AddScoped<ITotpProvider, TotpProvider>();

        // Register Redis
        services.AddRedis(configuration);

        // Register DbContext
        services.AddScoped<UpdateAuditableInterceptor>();
        services.AddDbContext<ApplicationDbContext>((sp, options) => options
            .UseNpgsql(configuration
                .GetConnectionString("PostgresConnectionString"), b => b
                .MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName))
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .AddInterceptors(
                sp.GetRequiredService<UpdateAuditableInterceptor>()
            ));

        return services;
    }
}